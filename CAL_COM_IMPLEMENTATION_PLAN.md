# 🚀 Cal.com Self-Hosted Integration Plan
## Complete Calendly Replacement Strategy

---

## 📋 Project Overview

**Goal:** Replace Calendly with self-hosted Cal.com integration, fully branded and embedded within UpZera platform.

**Benefits:**
- ✅ No monthly fees (save €30/month)
- ✅ No "Powered by" branding
- ✅ Full control and customization
- ✅ Advanced features (team scheduling, multiple calendars, etc.)
- ✅ Production-ready solution
- ✅ Active community support

---

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UpZera Site   │    │  Cal.com Self   │    │ Google Calendar │
│                 │    │     Hosted      │    │      API        │
│ - Contact Page  │◄──►│                 │◄──►│                 │
│ - Chatbot       │    │ - Booking Logic │    │ - Event Storage │
│ - Custom UI     │    │ - Admin Panel   │    │ - Availability  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## 📅 Implementation Timeline

### Phase 1: Infrastructure Setup (Week 1)
- [ ] Set up Cal.com self-hosted instance
- [ ] Configure database (PostgreSQL/Supabase)
- [ ] Set up domain and SSL
- [ ] Basic authentication setup

### Phase 2: Calendar Integration (Week 2)
- [ ] Google Calendar OAuth setup
- [ ] Configure availability rules
- [ ] Set up event types
- [ ] Test booking flow

### Phase 3: UI Customization (Week 3)
- [ ] Brand customization (colors, fonts, logos)
- [ ] Custom CSS for UpZera theme
- [ ] Mobile responsiveness
- [ ] Embed widget styling

### Phase 4: Integration (Week 4)
- [ ] Embed Cal.com widgets in UpZera site
- [ ] Replace Calendly in contact page
- [ ] Replace Calendly in chatbot
- [ ] API integration for lead capture

### Phase 5: Testing & Launch (Week 5)
- [ ] End-to-end testing
- [ ] Performance optimization
- [ ] Go live and monitor

---

## 🛠️ Technical Implementation Steps

### Step 1: Cal.com Self-Hosting Setup

#### 1.1 Server Requirements
```bash
# Minimum requirements:
- 2 CPU cores
- 4GB RAM
- 20GB storage
- Ubuntu 20.04+ or similar

# Recommended hosting:
- DigitalOcean Droplet ($20/month)
- AWS EC2 t3.medium
- Hetzner Cloud CX21
```

#### 1.2 Installation Commands
```bash
# Clone Cal.com repository
git clone https://github.com/calcom/cal.com.git
cd cal.com

# Install dependencies
yarn install

# Set up environment variables
cp .env.example .env
# Configure database, email, etc.

# Build and start
yarn build
yarn start
```

#### 1.3 Environment Configuration
```env
# Database (use your existing Supabase)
DATABASE_URL=********************************/calcom

# NextAuth configuration
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=https://cal.yourdomain.com

# Google Calendar
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Email (use your existing Mailgun)
EMAIL_FROM=<EMAIL>
EMAIL_SERVER_HOST=smtp.mailgun.org
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-mailgun-password
```

### Step 2: Database Setup

#### 2.1 Use Existing Supabase Instance
```sql
-- Create Cal.com tables in your existing Supabase
-- Cal.com will handle migrations automatically
-- Just provide the DATABASE_URL
```

#### 2.2 Data Integration
```javascript
// Webhook to sync bookings with your existing leads table
// POST /api/webhooks/booking-created
{
  "event": "booking.created",
  "data": {
    "id": "booking-id",
    "attendees": [{"email": "<EMAIL>", "name": "John Doe"}],
    "startTime": "2024-01-15T10:00:00Z",
    "endTime": "2024-01-15T10:30:00Z"
  }
}
```

### Step 3: Custom Branding

#### 3.1 Theme Customization
```css
/* Custom CSS for UpZera branding */
:root {
  --cal-brand-color: #7c3aed; /* Purple theme */
  --cal-brand-text-color: #ffffff;
  --cal-bg-emphasis: #1f2937;
  --cal-border: #374151;
}

/* Custom logo */
.cal-logo {
  content: url('/path/to/upzera-logo.svg');
}

/* Button styling */
.cal-button-primary {
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  border: none;
  border-radius: 8px;
}
```

#### 3.2 Logo and Assets
```bash
# Replace Cal.com assets with UpZera branding
/public/cal-logo.svg -> upzera-logo.svg
/public/favicon.ico -> upzera-favicon.ico
```

### Step 4: Embed Integration

#### 4.1 Contact Page Integration
```tsx
// Replace existing BookingCalendar component
import Cal, { getCalApi } from "@calcom/embed-react";

export default function ContactPage() {
  useEffect(() => {
    (async function () {
      const cal = await getCalApi();
      cal("ui", {
        "theme": "dark",
        "styles": {"branding": {"brandColor": "#7c3aed"}},
        "hideEventTypeDetails": false
      });
    })();
  }, []);

  return (
    <div className="cal-embed-container">
      <Cal
        calLink="your-username/30min"
        style={{width:"100%",height:"100%",overflow:"scroll"}}
        config={{"layout":"month_view"}}
      />
    </div>
  );
}
```

#### 4.2 Chatbot Widget Integration
```tsx
// Compact embed for chatbot
<Cal
  calLink="your-username/30min"
  style={{width:"320px",height:"400px"}}
  config={{
    "layout": "column_view",
    "theme": "light"
  }}
/>
```

### Step 5: Advanced Configuration

#### 5.1 Event Types Setup
```javascript
// Configure via Cal.com admin panel or API
{
  "title": "Consultation Call",
  "slug": "consultation",
  "length": 30,
  "description": "Free consultation with UpZera team",
  "locations": [
    {
      "type": "integrations:zoom",
      "displayLocationPublicly": true
    }
  ],
  "availability": {
    "schedule": "working-hours"
  }
}
```

#### 5.2 Webhook Configuration
```javascript
// Set up webhooks for integration
const webhookEndpoints = [
  {
    "subscriberUrl": "https://upzera.com/api/webhooks/cal-booking",
    "eventTriggers": [
      "BOOKING_CREATED",
      "BOOKING_CANCELLED",
      "BOOKING_RESCHEDULED"
    ]
  }
];
```

---

## 🔧 Integration Points

### Current UpZera → Cal.com Migration

#### Replace These Components:
```
❌ components/calendar/BookingCalendar.tsx
❌ components/calendar/ChatbotBookingWidget.tsx
❌ app/api/calendar/* (all custom API routes)
❌ lib/google-calendar.ts
❌ lib/availability.ts

✅ Cal.com embed components
✅ Cal.com webhook handlers
✅ Cal.com admin configuration
```

#### Keep These Components:
```
✅ Email notification styling (customize Cal.com templates)
✅ Database lead storage (via webhooks)
✅ Existing UI/UX patterns
✅ Supabase integration
```

---

## 🚀 Deployment Strategy

### Option 1: Subdomain Deployment
```
cal.upzera.com → Cal.com instance
upzera.com → Main site with embeds
```

### Option 2: Path-based Deployment
```
upzera.com/book → Cal.com instance
upzera.com → Main site with embeds
```

### Option 3: Docker Deployment
```dockerfile
# Use Cal.com official Docker image
FROM calcom/cal.com:latest

# Custom configuration
COPY custom-theme.css /app/styles/
COPY upzera-logo.svg /app/public/

# Environment variables
ENV NEXTAUTH_URL=https://cal.upzera.com
ENV DATABASE_URL=postgresql://...
```

---

## 📊 Success Metrics

### Technical Metrics:
- [ ] Page load time < 2 seconds
- [ ] Mobile responsiveness score > 95
- [ ] Booking completion rate > 80%
- [ ] Zero downtime deployment

### Business Metrics:
- [ ] Cost savings: €30/month (€360/year)
- [ ] Brand consistency: 100% UpZera branding
- [ ] Feature completeness: All Calendly features + more
- [ ] User satisfaction: Seamless booking experience

---

## 🔗 Resources & Documentation

### Cal.com Resources:
- [Self-hosting Guide](https://cal.com/docs/self-hosting)
- [Embed Documentation](https://cal.com/docs/embed)
- [API Reference](https://cal.com/docs/api)
- [Theming Guide](https://cal.com/docs/theming)

### Implementation Support:
- [Cal.com GitHub](https://github.com/calcom/cal.com)
- [Community Discord](https://cal.com/discord)
- [Documentation](https://cal.com/docs)

---

## 🎯 Next Steps

1. **Review this plan** and adjust timeline based on priorities
2. **Set up development environment** for Cal.com
3. **Configure hosting infrastructure** (DigitalOcean/AWS)
4. **Start with Phase 1** - basic Cal.com setup
5. **Iterate and customize** based on UpZera needs

This plan gives you a complete roadmap to replace Calendly with a fully-featured, self-hosted Cal.com solution that's perfectly integrated with your UpZera platform! 🚀

---

## 📝 Quick Start Checklist

### Before You Begin:
- [ ] Choose hosting provider (DigitalOcean recommended)
- [ ] Set up subdomain: `cal.upzera.com`
- [ ] Prepare Google Calendar API credentials
- [ ] Review current Supabase database structure

### Week 1 Priority Tasks:
1. [ ] Clone Cal.com repository
2. [ ] Set up server environment
3. [ ] Configure database connection
4. [ ] Get basic Cal.com instance running
5. [ ] Set up SSL certificate

### Key Decision Points:
- **Hosting**: DigitalOcean vs AWS vs Hetzner
- **Domain**: Subdomain vs path-based routing
- **Database**: New instance vs existing Supabase
- **Integration**: Full replacement vs gradual migration

### Success Criteria:
✅ Cal.com instance accessible at cal.upzera.com
✅ Google Calendar integration working
✅ Basic booking flow functional
✅ UpZera branding applied
✅ Embedded widgets working on main site

---

## 💡 Pro Tips for Implementation

1. **Start Simple**: Get basic Cal.com running first, then customize
2. **Use Staging**: Set up staging.cal.upzera.com for testing
3. **Backup Strategy**: Regular database backups before major changes
4. **Monitor Performance**: Set up monitoring from day one
5. **Document Changes**: Keep track of customizations for future updates

Ready to build the perfect Calendly replacement! 🎯
