# Custom Calendar Integration Setup Guide

This guide will help you set up the custom Google Calendar integration to replace <PERSON><PERSON>ly.

## 🎯 What's Been Implemented

✅ **Google Calendar API Integration**
- OAuth 2.0 authentication flow
- Calendar availability checking
- Event creation, updating, and deletion
- Token refresh handling

✅ **Custom Booking UI**
- Full-featured calendar component for the contact page
- Compact booking widget for the chatbot
- Responsive design matching your brand

✅ **Database Schema**
- Calendar tokens storage
- Booking management
- Settings configuration

✅ **Email Notifications**
- Booking confirmations
- Cancellation notifications
- Reminder emails (ready for cron job setup)

✅ **API Endpoints**
- `/api/auth/google` - Start OAuth flow
- `/api/auth/google/callback` - Handle OAuth callback
- `/api/calendar/availability` - Get available time slots
- `/api/calendar/book` - Create new bookings
- `/api/calendar/bookings` - Manage existing bookings

## 🚀 Setup Instructions

### 1. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Calendar API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Calendar API"
   - Click "Enable"

4. Create OAuth 2.0 credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Add authorized redirect URIs:
     - `http://localhost:3000/api/auth/google/callback` (for development)
     - `https://yourdomain.com/api/auth/google/callback` (for production)

5. Copy the Client ID and Client Secret

### 2. Environment Variables

Update your `.env.local` file with the Google Calendar credentials:

```env
# Google Calendar API Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3000/api/auth/google/callback

# Email Configuration (optional but recommended)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=UpZera
```

### 3. Database Setup

Run the SQL commands in `database/supabase-schema.sql` in your Supabase dashboard:

1. Go to your Supabase project dashboard
2. Navigate to "SQL Editor"
3. Copy and paste the contents of `database/supabase-schema.sql`
4. Run the query to create the necessary tables

### 4. Google Calendar Authentication

1. Start your development server: `npm run dev`
2. Visit: `http://localhost:3000/api/auth/google`
3. Follow the OAuth flow to connect your Google Calendar
4. You'll be redirected back to the contact page

### 5. Testing the Integration

1. Visit the contact page: `http://localhost:3000/contact`
2. Scroll down to the "Schedule a Meeting" section
3. You should see the custom calendar instead of Calendly
4. Try booking a test appointment

## 📧 Email Setup (Optional)

For email notifications, you'll need to configure SMTP:

### Gmail Setup:
1. Enable 2-factor authentication on your Google account
2. Generate an "App Password":
   - Go to Google Account settings
   - Security > 2-Step Verification > App passwords
   - Generate a password for "Mail"
3. Use this app password in the `EMAIL_PASS` environment variable

### Other Email Providers:
Update the `EMAIL_HOST` and `EMAIL_PORT` variables accordingly.

## 🎨 Customization

### Calendar Settings
Modify the default settings in the database:

```sql
UPDATE calendar_settings SET 
  working_hours_start = 9,
  working_hours_end = 17,
  slot_duration = 30,
  timezone = 'Europe/Vilnius',
  allow_weekends = false
WHERE id = 'default';
```

### UI Styling
The calendar components use your existing design system. You can customize:
- `components/calendar/BookingCalendar.tsx` - Main calendar component
- `components/calendar/ChatbotBookingWidget.tsx` - Chatbot widget

## 🔧 Troubleshooting

### Common Issues:

1. **"Calendar not connected" error**
   - Make sure you've completed the OAuth flow
   - Check that tokens are stored in the `calendar_tokens` table

2. **No available slots showing**
   - Verify your Google Calendar has events to check against
   - Check the `calendar_settings` table for correct working hours

3. **Email notifications not working**
   - Verify SMTP credentials in environment variables
   - Check server logs for email errors

4. **OAuth redirect errors**
   - Ensure redirect URI matches exactly in Google Cloud Console
   - Check that the domain is authorized

## 🚀 Deployment

For production deployment:

1. Update the `GOOGLE_REDIRECT_URI` to your production domain
2. Add the production redirect URI to Google Cloud Console
3. Update environment variables in your hosting platform
4. Ensure database tables are created in production

## 📊 Benefits Over Calendly

✅ **No Monthly Fees** - Completely free to use
✅ **No External Branding** - Fully branded to your company
✅ **Full Control** - Customize everything to your needs
✅ **Data Ownership** - All booking data stays in your database
✅ **Custom Workflows** - Integrate with your existing systems
✅ **Better Performance** - No external scripts to load

## 🔄 Migration from Calendly

The new system is now active on:
- Contact page (`/contact`)
- Chatbot booking widget

The old Calendly integration has been completely removed, so you can:
1. Cancel your Calendly subscription
2. Update any external links that pointed to Calendly
3. Enjoy your new custom calendar system!

## 📞 Support

If you encounter any issues during setup, check the browser console and server logs for error messages. The system includes comprehensive error handling and logging to help with troubleshooting.
