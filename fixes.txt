
General fixes that have to be made to achieve final website goal:



fix calendly in netlify hosted website

integrate google api calendar with customizable UI (bigger fix)

explore options to make the ROBOT faster in chatbot page

optimise website for speed if needed.

understand whether <PERSON> wants to launch or no, and also send him the qoutes that we wrote and possibly ask what he would want to write.

image in email templates should be hosted on github or via some https server, to avoid the display  confirmations


