// API endpoint to manually refresh the knowledge base
import { NextApiRequest, NextApiResponse } from 'next';
import { KnowledgeScraper } from '../../lib/knowledge-scraper';
import { KnowledgeCacheManager } from '../../lib/knowledge-cache-manager';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Add CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { force = false } = req.body;

    const scraper = KnowledgeScraper.getInstance();
    const cacheManager = new KnowledgeCacheManager();

    const urlsToScrape = [
      'https://upzera-web.netlify.app',
      'https://upzera-web.netlify.app/about',
      'https://upzera-web.netlify.app/our-approach',
      'https://upzera-web.netlify.app/testimonials',
      'https://upzera-web.netlify.app/faq',
      'https://upzera-web.netlify.app/contact',
      'https://upzera-web.netlify.app/website-development',
      'https://upzera-web.netlify.app/chatbot-integration'
    ];

    if (force) {
      // Force refresh - clear cache and rebuild
      await cacheManager.clearCache();
      await scraper.forceRefresh(urlsToScrape);
    } else {
      // Try to load from file cache first
      const cachedKnowledge = await cacheManager.loadFromCache();
      if (cachedKnowledge) {
        // Load cached data into scraper
        scraper.loadKnowledgeBase(cachedKnowledge);
      } else {
        // Build fresh and save to cache
        await scraper.buildKnowledgeBase(urlsToScrape);
        await cacheManager.saveToCache(scraper.getKnowledgeBase());
      }
    }

    const stats = scraper.getStats();

    res.status(200).json({
      success: true,
      message: force ? 'Knowledge base force refreshed' : 'Knowledge base refreshed',
      stats,
      cacheStats: cacheManager.getCacheStats(),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Knowledge refresh error:', error);
    res.status(500).json({
      error: 'Failed to refresh knowledge base',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
