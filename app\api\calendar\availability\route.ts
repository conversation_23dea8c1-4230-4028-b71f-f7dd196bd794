import { NextRequest, NextResponse } from 'next/server';
import { createCalendarClient, refreshAccessToken } from '@/lib/google-calendar';
import { supabase } from '@/lib/supabase';
import {
  getCalendarSettings,
  generateTimeSlots,
  filterBusySlots,
  getExistingBookings,
  combineBusyTimes
} from '@/lib/availability';

// GET /api/calendar/availability - Get available booking slots
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');
  const duration = parseInt(searchParams.get('duration') || '30');
  
  if (!startDate || !endDate) {
    return NextResponse.json(
      { error: 'startDate and endDate are required' },
      { status: 400 }
    );
  }
  
  try {
    // Get stored tokens from Supabase
    const { data: tokenData, error: tokenError } = await supabase
      .from('calendar_tokens')
      .select('*')
      .eq('id', 'admin')
      .single();
    
    if (tokenError || !tokenData) {
      return NextResponse.json(
        { error: 'Calendar not connected. Please authenticate first.' },
        { status: 401 }
      );
    }
    
    let accessToken = tokenData.access_token;
    let refreshToken = tokenData.refresh_token;
    
    // Check if token needs refresh
    if (tokenData.expires_at && new Date(tokenData.expires_at) <= new Date()) {
      try {
        const newTokens = await refreshAccessToken(refreshToken);
        accessToken = newTokens.access_token!;
        
        // Update tokens in database
        await supabase
          .from('calendar_tokens')
          .update({
            access_token: accessToken,
            expires_at: newTokens.expiry_date ? new Date(newTokens.expiry_date) : null,
            updated_at: new Date()
          })
          .eq('id', 'admin');
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        return NextResponse.json(
          { error: 'Calendar authentication expired. Please re-authenticate.' },
          { status: 401 }
        );
      }
    }
    
    // Get calendar settings
    const settings = await getCalendarSettings();

    // Generate all possible time slots
    const allSlots = generateTimeSlots(
      new Date(startDate),
      new Date(endDate),
      settings
    );

    // Get busy times from Google Calendar
    const calendar = createCalendarClient(accessToken, refreshToken);
    const freeBusyResponse = await calendar.freebusy.query({
      requestBody: {
        timeMin: new Date(startDate).toISOString(),
        timeMax: new Date(endDate).toISOString(),
        items: [{ id: 'primary' }]
      }
    });

    const googleBusyTimes = freeBusyResponse.data.calendars?.['primary']?.busy || [];

    // Get existing bookings from our database
    const localBookings = await getExistingBookings(
      new Date(startDate),
      new Date(endDate)
    );

    // Combine all busy times
    const allBusyTimes = combineBusyTimes(
      googleBusyTimes.map(bt => ({ start: bt.start!, end: bt.end! })),
      localBookings
    );

    // Filter out busy slots
    const availableSlots = filterBusySlots(
      allSlots,
      allBusyTimes,
      settings.buffer_time
    );

    return NextResponse.json({ slots: availableSlots });
    
  } catch (error) {
    console.error('Error getting availability:', error);
    return NextResponse.json(
      { error: 'Failed to get availability' },
      { status: 500 }
    );
  }
}
